package sirobilt.meghasanjivini.abhaservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.abhaservice.client.AbdmProfileClient
import sirobilt.meghasanjivini.abhaservice.dto.SearchAbhaRequest
import sirobilt.meghasanjivini.abhaservice.dto.SearchAbhaResponseDTO
import sirobilt.meghasanjivini.abhaservice.util.RsaEncryptor

@ApplicationScoped
class AbhaSearchService {

    private val logger = Logger.getLogger(AbhaSearchService::class.java)

    @ConfigProperty(name = "abdm.benefit-name")
    lateinit var benefitName: String

    @Inject
    lateinit var objectMapper: ObjectMapper

    @Inject
    lateinit var sessionService: AbdmSessionService

    @Inject
    lateinit var publicKeyService: AbdmPublicKeyService

    @Inject
    @RestClient
    lateinit var profileClient: AbdmProfileClient

    /**
     * Initiates ABHA search flow using mobile number.
     * Uses cached session tokens and public keys for optimal performance.
     */
    fun initiateAbhaSearchFlow(plainMobile: String): SearchAbhaResponseDTO {
        logger.info("Initiating ABHA search flow for mobile number")

        try {
            // Step 1: Get cached session token (performance optimized)
            val accessToken = sessionService.getSessionToken()

            // Step 2: Get cached public key (performance optimized)
            val publicKey = publicKeyService.getPublicKey()

            // Step 3: Encrypt Mobile Number
            val encryptedMobile = RsaEncryptor.encrypt(plainMobile, publicKey)

            // Step 4: Prepare search request
            val requestId = sessionService.generateRequestId()
            val timestamp = sessionService.getCurrentTimestamp()
            val searchRequest = SearchAbhaRequest(
                scope = listOf("search-abha"),
                mobile = encryptedMobile
            )

            logger.debug("Sending ABHA search request to ABDM")

            // Step 5: Call Search API
            val searchResponse = profileClient.searchAbha(
                body = searchRequest,
                requestId = requestId,
                timestamp = timestamp,
                token = "Bearer $accessToken",
                benefitName = benefitName
            )

            if (searchResponse.status != 200) {
                throw AbhaSearchException(
                    "ABHA search failed: HTTP ${searchResponse.status}",
                    searchResponse.status
                )
            }

            val result = searchResponse.readEntity(SearchAbhaResponseDTO::class.java)
            logger.info("ABHA search completed successfully")
            return result

        } catch (e: Exception) {
            logger.error("Failed to complete ABHA search", e)
            when (e) {
                is AbhaSearchException -> throw e
                else -> throw AbhaSearchException("Unexpected error during ABHA search", 500, e)
            }
        }
    }
}

/**
 * Custom exception for ABHA search-related errors
 */
class AbhaSearchException(
    message: String,
    val statusCode: Int = 500,
    cause: Throwable? = null
) : RuntimeException(message, cause)
}
