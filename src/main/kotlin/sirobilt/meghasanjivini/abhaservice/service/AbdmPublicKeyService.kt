package sirobilt.meghasanjivini.abhaservice.service

import io.quarkus.cache.CacheResult
import io.quarkus.cache.CacheKey
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.abhaservice.client.AbdmPublicKeyClient

/**
 * Centralized service for managing ABDM public keys with caching.
 * Public keys are cached for 1 hour since they change infrequently.
 */
@ApplicationScoped
class AbdmPublicKeyService {

    private val logger = Logger.getLogger(AbdmPublicKeyService::class.java)

    @Inject
    @RestClient
    lateinit var publicKeyClient: AbdmPublicKeyClient

    @Inject
    lateinit var sessionService: AbdmSessionService

    /**
     * Gets a cached public key from ABDM. Cache key includes environment
     * to handle sandbox vs production keys separately.
     */
    @CacheResult(cacheName = "abdm-public-keys", lockTimeout = 30000)
    fun getPublicKey(@CacheKey environment: String = sessionService.environment): String {
        logger.info("Fetching ABDM public key for environment: $environment")
        
        try {
            val accessToken = sessionService.getSessionToken()
            val requestId = sessionService.generateRequestId()
            val timestamp = sessionService.getCurrentTimestamp()
            val cmId = sessionService.getCmId()

            val response = publicKeyClient.getPublicKey(
                requestId = requestId,
                timestamp = timestamp,
                cmId = cmId,
                authorization = "Bearer $accessToken"
            )

            if (response.status != 200) {
                throw AbdmPublicKeyException(
                    "Failed to fetch public key: HTTP ${response.status}",
                    response.status
                )
            }

            val responseBody = response.readEntity(Map::class.java)
            val publicKey = responseBody["publicKey"] as? String
                ?: throw AbdmPublicKeyException("Public key not found in response", response.status)

            logger.info("Successfully fetched ABDM public key")
            return publicKey

        } catch (e: Exception) {
            logger.error("Failed to fetch ABDM public key", e)
            when (e) {
                is AbdmPublicKeyException -> throw e
                else -> throw AbdmPublicKeyException("Unexpected error fetching public key", 500, e)
            }
        }
    }
}

/**
 * Custom exception for ABDM public key-related errors
 */
class AbdmPublicKeyException(
    message: String,
    val statusCode: Int = 500,
    cause: Throwable? = null
) : RuntimeException(message, cause)
