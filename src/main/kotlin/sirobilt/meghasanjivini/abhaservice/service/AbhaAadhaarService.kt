package sirobilt.meghasanjivini.abhaservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.abhaservice.client.AbdmOtpClient
import sirobilt.meghasanjivini.abhaservice.dto.AbhaAadhaarOtpResponseDTO
import sirobilt.meghasanjivini.abhaservice.dto.OtpRequest
import sirobilt.meghasanjivini.abhaservice.util.RsaEncryptor



@ApplicationScoped
class AbhaAadhaarService {

    private val logger = Logger.getLogger(AbhaAadhaarService::class.java)

    @Inject
    lateinit var objectMapper: ObjectMapper

    @Inject
    lateinit var sessionService: AbdmSessionService

    @Inject
    lateinit var publicKeyService: AbdmPublicKeyService

    @Inject
    @RestClient
    lateinit var otpClient: AbdmOtpClient

    /**
     * Initiates ABHA OTP flow using <PERSON><PERSON><PERSON><PERSON> number.
     * Uses cached session tokens and public keys for optimal performance.
     */
    fun initiateAbhaOtpFlow(aadhaar: String): AbhaAadhaarOtpResponseDTO {
        logger.info("Initiating ABHA OTP flow for Aadhaar")

        try {
            // Step 1: Get cached session token (no recreation overhead)
            val accessToken = sessionService.getSessionToken()

            // Step 2: Get cached public key (no repeated API calls)
            val publicKey = publicKeyService.getPublicKey()

            // Step 3: Encrypt Aadhaar Number
            val encryptedAadhaar = RsaEncryptor.encrypt(aadhaar, publicKey)

            // Step 4: Prepare OTP request
            val requestId = sessionService.generateRequestId()
            val timestamp = sessionService.getCurrentTimestamp()
            val cmId = sessionService.getCmId()


            // Step 5: Create OTP request
            val otpRequest = OtpRequest(
                txnId = null,
                scope = listOf("abha-enrol"),
                loginHint = "aadhaar",
                loginId = encryptedAadhaar,
                otpSystem = "aadhaar"
            )

            // Step 6: Call OTP API (using injected ObjectMapper for performance)
            logger.debug("Sending OTP request to ABDM")
            val otpResponse = otpClient.requestOtp(
                otpRequest,
                requestId,
                timestamp,
                cmId,
                "Bearer $accessToken"
            )

            if (otpResponse.status != 200) {
                throw AbhaOtpException(
                    "Failed to request OTP: HTTP ${otpResponse.status}",
                    otpResponse.status
                )
            }

            val responseBody = otpResponse.readEntity(Map::class.java)

            // Step 7: Build and return response
            val result = AbhaAadhaarOtpResponseDTO().apply {
                txnId = responseBody["txnId"] as? String
                message = responseBody["message"] as? String ?: "OTP request initiated successfully"
            }

            logger.info("ABHA OTP flow initiated successfully")
            return result

        } catch (e: Exception) {
            logger.error("Failed to initiate ABHA OTP flow", e)
            when (e) {
                is AbhaOtpException -> throw e
                else -> throw AbhaOtpException("Unexpected error during OTP initiation", 500, e)
            }
        }
    }
}

/**
 * Custom exception for ABHA OTP-related errors
 */
class AbhaOtpException(
    message: String,
    val statusCode: Int = 500,
    cause: Throwable? = null
) : RuntimeException(message, cause)
