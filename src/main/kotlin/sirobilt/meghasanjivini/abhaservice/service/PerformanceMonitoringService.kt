package sirobilt.meghasanjivini.abhaservice.service

import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.jboss.logging.Logger

/**
 * Service for monitoring performance metrics related to ABDM operations.
 * Tracks cache hits, API call durations, and success/failure rates.
 */
@ApplicationScoped
class PerformanceMonitoringService {

    private val logger = Logger.getLogger(PerformanceMonitoringService::class.java)

    @Inject
    lateinit var meterRegistry: MeterRegistry

    // Cache performance metrics
    private val sessionCacheHits by lazy {
        Counter.builder("abdm.session.cache.hits")
            .description("Number of session token cache hits")
            .register(meterRegistry)
    }

    private val sessionCacheMisses by lazy {
        Counter.builder("abdm.session.cache.misses")
            .description("Number of session token cache misses")
            .register(meterRegistry)
    }

    private val publicKeyCacheHits by lazy {
        Counter.builder("abdm.publickey.cache.hits")
            .description("Number of public key cache hits")
            .register(meterRegistry)
    }

    private val publicKeyCacheMisses by lazy {
        Counter.builder("abdm.publickey.cache.misses")
            .description("Number of public key cache misses")
            .register(meterRegistry)
    }

    // API performance metrics
    private val abhaOtpTimer by lazy {
        Timer.builder("abdm.abha.otp.duration")
            .description("Time taken for ABHA OTP operations")
            .register(meterRegistry)
    }

    private val abhaSearchTimer by lazy {
        Timer.builder("abdm.abha.search.duration")
            .description("Time taken for ABHA search operations")
            .register(meterRegistry)
    }

    private val abhaEnrollmentTimer by lazy {
        Timer.builder("abdm.abha.enrollment.duration")
            .description("Time taken for ABHA enrollment operations")
            .register(meterRegistry)
    }

    // Success/failure counters
    private val abhaOtpSuccess by lazy {
        Counter.builder("abdm.abha.otp.success")
            .description("Number of successful ABHA OTP operations")
            .register(meterRegistry)
    }

    private val abhaOtpFailure by lazy {
        Counter.builder("abdm.abha.otp.failure")
            .description("Number of failed ABHA OTP operations")
            .register(meterRegistry)
    }

    // Cache monitoring methods
    fun recordSessionCacheHit() {
        sessionCacheHits.increment()
        logger.debug("Session cache hit recorded")
    }

    fun recordSessionCacheMiss() {
        sessionCacheMisses.increment()
        logger.debug("Session cache miss recorded")
    }

    fun recordPublicKeyCacheHit() {
        publicKeyCacheHits.increment()
        logger.debug("Public key cache hit recorded")
    }

    fun recordPublicKeyCacheMiss() {
        publicKeyCacheMisses.increment()
        logger.debug("Public key cache miss recorded")
    }

    // API timing methods
    fun <T> timeAbhaOtpOperation(operation: () -> T): T {
        return abhaOtpTimer.recordCallable(operation)!!
    }

    fun <T> timeAbhaSearchOperation(operation: () -> T): T {
        return abhaSearchTimer.recordCallable(operation)!!
    }

    fun <T> timeAbhaEnrollmentOperation(operation: () -> T): T {
        return abhaEnrollmentTimer.recordCallable(operation)!!
    }

    // Success/failure tracking
    fun recordAbhaOtpSuccess() {
        abhaOtpSuccess.increment()
        logger.debug("ABHA OTP success recorded")
    }

    fun recordAbhaOtpFailure() {
        abhaOtpFailure.increment()
        logger.debug("ABHA OTP failure recorded")
    }

    /**
     * Logs current cache performance statistics
     */
    fun logCacheStatistics() {
        val sessionHitRate = calculateHitRate(sessionCacheHits.count(), sessionCacheMisses.count())
        val publicKeyHitRate = calculateHitRate(publicKeyCacheHits.count(), publicKeyCacheMisses.count())
        
        logger.info("Cache Performance - Session Hit Rate: ${sessionHitRate}%, Public Key Hit Rate: ${publicKeyHitRate}%")
    }

    private fun calculateHitRate(hits: Double, misses: Double): Double {
        val total = hits + misses
        return if (total > 0) (hits / total) * 100 else 0.0
    }
}
