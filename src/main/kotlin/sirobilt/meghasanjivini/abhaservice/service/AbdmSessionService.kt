package sirobilt.meghasanjivini.abhaservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.quarkus.cache.CacheResult
import io.quarkus.cache.CacheKey
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import org.eclipse.microprofile.config.inject.ConfigProperty
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.abhaservice.client.AbdmSessionClient
import sirobilt.meghasanjivini.abhaservice.dto.SessionRequest
import java.time.Instant
import java.util.*

/**
 * Centralized service for managing ABDM session tokens with caching.
 * Implements caching to avoid creating new session tokens for every request.
 */
@ApplicationScoped
class AbdmSessionService {

    private val logger = Logger.getLogger(AbdmSessionService::class.java)

    @ConfigProperty(name = "abdm.client-id")
    lateinit var clientId: String

    @ConfigProperty(name = "abdm.client-secret")
    lateinit var clientSecret: String

    @ConfigProperty(name = "abdm.environment")
    lateinit var environment: String

    @Inject
    @RestClient
    lateinit var sessionClient: AbdmSessionClient

    @Inject
    lateinit var objectMapper: ObjectMapper

    /**
     * Gets a cached session token. Cache key includes client credentials to handle
     * multiple environments. Cache expires automatically after 15 minutes.
     */
    @CacheResult(cacheName = "abdm-session-tokens", lockTimeout = 30000)
    fun getSessionToken(@CacheKey clientId: String = this.clientId): String {
        logger.info("Creating new ABDM session token for client: $clientId")
        
        val requestId = UUID.randomUUID().toString()
        val timestamp = Instant.now().toString()
        val cmId = if (environment == "sandbox") "sbx" else "abdm"

        try {
            val sessionRequest = SessionRequest(clientId, clientSecret, "client_credentials")
            
            val response = sessionClient.createSession(
                body = sessionRequest,
                requestId = requestId,
                timestamp = timestamp,
                cmId = cmId,
                accept = "application/json"
            )

            if (response.status !in listOf(200, 202)) {
                throw AbdmSessionException(
                    "Failed to create session token: HTTP ${response.status}",
                    response.status
                )
            }

            val responseBody = response.readEntity(Map::class.java)
            val accessToken = responseBody["accessToken"] as? String
                ?: throw AbdmSessionException("Access token not found in response", response.status)

            logger.info("Successfully created ABDM session token")
            return accessToken

        } catch (e: Exception) {
            logger.error("Failed to create ABDM session token", e)
            when (e) {
                is AbdmSessionException -> throw e
                else -> throw AbdmSessionException("Unexpected error creating session token", 500, e)
            }
        }
    }

    /**
     * Gets the current environment-specific CM-ID
     */
    fun getCmId(): String = if (environment == "sandbox") "sbx" else "abdm"

    /**
     * Generates a new request ID for ABDM API calls
     */
    fun generateRequestId(): String = UUID.randomUUID().toString()

    /**
     * Gets current timestamp in ISO format for ABDM API calls
     */
    fun getCurrentTimestamp(): String = Instant.now().toString()
}

/**
 * Custom exception for ABDM session-related errors
 */
class AbdmSessionException(
    message: String,
    val statusCode: Int = 500,
    cause: Throwable? = null
) : RuntimeException(message, cause)
