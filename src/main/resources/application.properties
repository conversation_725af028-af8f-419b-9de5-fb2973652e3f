quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=${POSTGRES_DB_USERNAME}
quarkus.datasource.password=${POSTGRES_DB_PASSWORD}
quarkus.datasource.jdbc.url=jdbc:postgresql://${POSTGRES_DB_HOST}/${POSTGRES_DB_DATABASE}
quarkus.hibernate-orm.database.generation=update


quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS
quarkus.http.cors.headers=Content-Type,Authorization



quarkus.smallrye-openapi.path=/q/openapi
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/q/swagger-ui


quarkus.http.host=0.0.0.0
quarkus.http.port=8081

quarkus.oidc.enabled=false

quarkus.log.category."org.hibernate.type.descriptor.sql.BasicBinder".level=TRACE
quarkus.log.category."org.hibernate.SQL".level=DEBUG

#flyway
quarkus.flyway.migrate-at-start=true
quarkus.flyway.baseline-on-migrate=true
quarkus.flyway.locations=classpath:db/migration


quarkus.flyway.repair-at-start=true



# to prefix every HTTP endpoint (including health checks, swagger, static, etc.)
#quarkus.http.root-path=/api

# OR, if you only want to move your JAX-RS endpoints (and leave Swagger UI, health, metrics, etc at /q/*):
#quarkus.resteasy-reactive.root-path=/api

# Tell SmallRye where to load the private key for signing
# Windows absolute path (note the triple slash)
smallrye.jwt.sign.key.location=file:///D:/EHR-Quarkus/EHR-Server/src/main/resources/privateKey.pem

# src/main/resources/application.properties
quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy


# Removed problematic property: quarkus.datasource.agroal.connection-acquisition-timeout=PT30S

#--------------------------------------------------------------------

# ? ABDM credentials & env
abdm.client-id=${ABDM_CLIENT_ID}
abdm.client-secret=${ABDM_CLIENT_SECRET}


abdm.environment=sandbox


quarkus.jackson.default-property-inclusion=non_null


# configure MP-RestClient
quarkus.rest-client."com.example.abdm.AbdmGatewayClient".url=${abdm.${abdm.environment}.base-url}
# Base ABHA API host
abdm.api.base-url=https://abhasbx.abdm.gov.in/abha/api

quarkus.rest-client.abdm-session.url=https://dev.abdm.gov.in/api/hiecm/gateway/v3
quarkus.rest-client.abdm-publickey.url=https://abhasbx.abdm.gov.in/abha/api/v3/profile
quarkus.rest-client.abdm-otp.url= https://abhasbx.abdm.gov.in/abha/api/v3
quarkus.rest-client.abdm-enrollment.url=https://abhasbx.abdm.gov.in/abha/api/v3
# MicroProfile Rest Client config for the @RegisterRestClient(configKey="abdm-profile")
# ? this will target https://api.abdm.gov.in/v3/profile/account/abha
quarkus.rest-client.abdm-profile.url=https://abhasbx.abdm.gov.in/abha/api


quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.category."io.quarkus.rest.client".level=DEBUG
quarkus.log.category."io.vertx.core".level=DEBUG

abdm.benefit-name=dummy-benefit

# Cache Configuration for Performance Optimization
# ABDM Session Token Cache - expires after 15 minutes (tokens typically valid for 30 minutes)
quarkus.cache.caffeine."abdm-session-tokens".expire-after-write=15M
quarkus.cache.caffeine."abdm-session-tokens".maximum-size=100

# ABDM Public Key Cache - expires after 1 hour (keys change infrequently)
quarkus.cache.caffeine."abdm-public-keys".expire-after-write=1H
quarkus.cache.caffeine."abdm-public-keys".maximum-size=10

# Enable cache metrics for monitoring
quarkus.cache.caffeine."abdm-session-tokens".metrics-enabled=true
quarkus.cache.caffeine."abdm-public-keys".metrics-enabled=true

# Micrometer metrics configuration
quarkus.micrometer.enabled=true
quarkus.micrometer.export.prometheus.enabled=true
quarkus.micrometer.export.prometheus.path=/q/metrics

# Performance monitoring
quarkus.log.category."sirobilt.meghasanjivini.abhaservice.service".level=INFO

