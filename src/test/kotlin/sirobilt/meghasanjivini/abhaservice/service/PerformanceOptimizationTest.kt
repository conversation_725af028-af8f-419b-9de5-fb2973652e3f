package sirobilt.meghasanjivini.abhaservice.service

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import com.fasterxml.jackson.databind.ObjectMapper

/**
 * Test class to verify performance optimizations are working correctly.
 */
@QuarkusTest
class PerformanceOptimizationTest {

    @Inject
    lateinit var objectMapper: ObjectMapper

    @Inject
    lateinit var sessionService: AbdmSessionService

    @Inject
    lateinit var publicKeyService: AbdmPublicKeyService

    @Test
    fun `should inject singleton ObjectMapper`() {
        // Verify that ObjectMapper is properly injected as singleton
        assertNotNull(objectMapper)
        
        // Test that it's configured correctly
        assertFalse(objectMapper.isEnabled(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES))
    }

    @Test
    fun `should inject session service`() {
        // Verify that session service is properly injected
        assertNotNull(sessionService)
    }

    @Test
    fun `should inject public key service`() {
        // Verify that public key service is properly injected
        assertNotNull(publicKeyService)
    }

    @Test
    fun `should generate consistent request IDs`() {
        // Test utility methods
        val requestId1 = sessionService.generateRequestId()
        val requestId2 = sessionService.generateRequestId()
        
        assertNotNull(requestId1)
        assertNotNull(requestId2)
        assertNotEquals(requestId1, requestId2) // Should be unique
    }

    @Test
    fun `should generate timestamps`() {
        val timestamp = sessionService.getCurrentTimestamp()
        assertNotNull(timestamp)
        assertTrue(timestamp.contains("T")) // ISO format should contain 'T'
    }

    @Test
    fun `should determine correct CM-ID based on environment`() {
        val cmId = sessionService.getCmId()
        assertNotNull(cmId)
        // Should be either "sbx" for sandbox or "abdm" for production
        assertTrue(cmId == "sbx" || cmId == "abdm")
    }
}
